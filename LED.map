Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    main.o(i.Led) refers to main.o(.data) for .data
    main.o(i.main) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.main) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$********) for _printf_f
    main.o(i.main) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.main) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.main) refers to _printf_str.o(.text) for _printf_str
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to usart.o(i.uart1_init) for uart1_init
    main.o(i.main) refers to usart.o(i.uart3_init) for uart3_init
    main.o(i.main) refers to usart.o(i.uart2_init) for uart2_init
    main.o(i.main) refers to adc.o(i.Adc_Init) for Adc_Init
    main.o(i.main) refers to tim.o(i.tim2_init) for tim2_init
    main.o(i.main) refers to tim.o(i.tim4_init) for tim4_init
    main.o(i.main) refers to __2sprintf.o(.text) for __2sprintf
    main.o(i.main) refers to __2printf.o(.text) for __2printf
    main.o(i.main) refers to main.o(i.task_handler) for task_handler
    main.o(i.main) refers to main.o(.bss) for .bss
    main.o(i.task_handler) refers to main.o(.data) for .data
    main.o(i.task_schedule_callback) refers to main.o(.data) for .data
    main.o(i.tim2_IRQ) refers to adc.o(i.Get_Adc) for Get_Adc
    main.o(i.tim2_IRQ) refers to main.o(.data) for .data
    main.o(i.tim4_IRQ) refers to main.o(i.task_schedule_callback) for task_schedule_callback
    main.o(i.usart1_Rx_IRQ) refers to usart.o(i.USART_printf) for USART_printf
    main.o(i.usart1_Rx_IRQ) refers to main.o(.data) for .data
    main.o(i.usart1_send) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.usart1_send) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$********) for _printf_f
    main.o(i.usart1_send) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.usart1_send) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.usart1_send) refers to _printf_str.o(.text) for _printf_str
    main.o(i.usart1_send) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.usart1_send) refers to __2sprintf.o(.text) for __2sprintf
    main.o(i.usart1_send) refers to __2printf.o(.text) for __2printf
    main.o(i.usart1_send) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    main.o(i.usart1_send) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    main.o(i.usart1_send) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    main.o(i.usart1_send) refers to main.o(.data) for .data
    main.o(i.usart1_send) refers to main.o(.bss) for .bss
    main.o(i.usart2_Rx_IRQ) refers to main.o(i.Dis_get) for Dis_get
    main.o(i.usart2_Rx_IRQ) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    main.o(i.usart2_Rx_IRQ) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    main.o(i.usart2_Rx_IRQ) refers to usart.o(i.USART_printf) for USART_printf
    main.o(i.usart2_Rx_IRQ) refers to main.o(i.str_num) for str_num
    main.o(i.usart2_Rx_IRQ) refers to main.o(.data) for .data
    main.o(.data) refers to main.o(i.Led) for Led
    main.o(.data) refers to main.o(i.usart1_send) for usart1_send
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for .data
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    led.o(i.LED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.IIC_Start1) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.IIC_Start1) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.IIC_Stop1) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.IIC_Stop1) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.IIC_Wait_Ack1) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.IIC_Wait_Ack1) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DrawBMP) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_DrawBMP) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.OLED_Init) refers to delay.o(i.delay_ms) for delay_ms
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_SHOWCH) refers to oled.o(i.OLED_Set_Pixel) for OLED_Set_Pixel
    oled.o(i.OLED_SHOWCH) refers to oled.o(i.OLED_Display) for OLED_Display
    oled.o(i.OLED_SHOWCH) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_SHOWCHSTR) refers to oled.o(i.OLED_SHOWCH) for OLED_SHOWCH
    oled.o(i.OLED_Set_Pixel) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_Set_Pos) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowNum) refers to oled.o(i.oled_pow) for oled_pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.Write_IIC_Data) for Write_IIC_Data
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.Write_IIC_Command) for Write_IIC_Command
    oled.o(i.Write_IIC_Byte) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.Write_IIC_Byte) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.Write_IIC_Command) refers to oled.o(i.IIC_Start1) for IIC_Start1
    oled.o(i.Write_IIC_Command) refers to oled.o(i.Write_IIC_Byte) for Write_IIC_Byte
    oled.o(i.Write_IIC_Command) refers to oled.o(i.IIC_Wait_Ack1) for IIC_Wait_Ack1
    oled.o(i.Write_IIC_Command) refers to oled.o(i.IIC_Stop1) for IIC_Stop1
    oled.o(i.Write_IIC_Data) refers to oled.o(i.IIC_Start1) for IIC_Start1
    oled.o(i.Write_IIC_Data) refers to oled.o(i.Write_IIC_Byte) for Write_IIC_Byte
    oled.o(i.Write_IIC_Data) refers to oled.o(i.IIC_Wait_Ack1) for IIC_Wait_Ack1
    oled.o(i.Write_IIC_Data) refers to oled.o(i.IIC_Stop1) for IIC_Stop1
    oled.o(i.fill_picture) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    delay.o(i.delay_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    delay.o(i.delay_init) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(i.delay_init) refers to delay.o(.data) for .data
    delay.o(i.delay_ms) refers to delay.o(.data) for .data
    delay.o(i.delay_us) refers to delay.o(.data) for .data
    usart.o(i.USART1_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_IRQHandler) refers to main.o(i.usart1_Rx_IRQ) for usart1_Rx_IRQ
    usart.o(i.USART2_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART2_IRQHandler) refers to main.o(i.usart2_Rx_IRQ) for usart2_Rx_IRQ
    usart.o(i.USART3_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART3_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART3_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART3_IRQHandler) refers to usart.o(i.usart3_Rx_IRQ) for usart3_Rx_IRQ
    usart.o(i.USART_printf) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART_printf) refers to vsnprintf.o(.text) for vsnprintf
    usart.o(i.USART_printf) refers to usart.o(i.USART_sendBuf) for USART_sendBuf
    usart.o(i.USART_sendBuf) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i._ttywrch) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart1_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart1_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart1_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart1_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.uart1_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart1_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart1_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.uart2_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart2_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart2_init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart.o(i.uart2_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart2_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.uart2_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart2_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart2_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.uart3_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart3_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart3_init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart.o(i.uart3_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart3_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.uart3_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart3_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart3_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.usart1_Rx_IRQ) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.usart2_Rx_IRQ) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.usart3_Rx_IRQ) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    adc.o(i.Adc_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    adc.o(i.Adc_Init) refers to stm32f10x_rcc.o(i.RCC_ADCCLKConfig) for RCC_ADCCLKConfig
    adc.o(i.Adc_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_DeInit) for ADC_DeInit
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_Init) for ADC_Init
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_Cmd) for ADC_Cmd
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_ResetCalibration) for ADC_ResetCalibration
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus) for ADC_GetResetCalibrationStatus
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_StartCalibration) for ADC_StartCalibration
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_GetCalibrationStatus) for ADC_GetCalibrationStatus
    adc.o(i.Get_Adc) refers to stm32f10x_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    adc.o(i.Get_Adc) refers to stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd) for ADC_SoftwareStartConvCmd
    adc.o(i.Get_Adc) refers to stm32f10x_adc.o(i.ADC_GetFlagStatus) for ADC_GetFlagStatus
    adc.o(i.Get_Adc) refers to stm32f10x_adc.o(i.ADC_GetConversionValue) for ADC_GetConversionValue
    adc.o(i.Get_Adc_Average) refers to adc.o(i.Get_Adc) for Get_Adc
    adc.o(i.Get_Adc_Average) refers to delay.o(i.delay_ms) for delay_ms
    tim.o(i.TIM1_UP_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    tim.o(i.TIM1_UP_IRQHandler) refers to tim.o(i.tim1_IRQ) for tim1_IRQ
    tim.o(i.TIM1_UP_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    tim.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    tim.o(i.TIM2_IRQHandler) refers to main.o(i.tim2_IRQ) for tim2_IRQ
    tim.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    tim.o(i.TIM3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    tim.o(i.TIM3_IRQHandler) refers to tim.o(i.tim3_IRQ) for tim3_IRQ
    tim.o(i.TIM3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    tim.o(i.TIM4_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    tim.o(i.TIM4_IRQHandler) refers to main.o(i.tim4_IRQ) for tim4_IRQ
    tim.o(i.TIM4_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    tim.o(i.tim1_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    tim.o(i.tim1_init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tim.o(i.tim1_init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    tim.o(i.tim1_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    tim.o(i.tim1_init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    tim.o(i.tim2_init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    tim.o(i.tim2_init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tim.o(i.tim2_init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    tim.o(i.tim2_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    tim.o(i.tim2_init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    tim.o(i.tim3_init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    tim.o(i.tim3_init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tim.o(i.tim3_init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    tim.o(i.tim3_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    tim.o(i.tim3_init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    tim.o(i.tim4_init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    tim.o(i.tim4_init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tim.o(i.tim4_init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    tim.o(i.tim4_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    tim.o(i.tim4_init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    startup_stm32f10x_md.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(RESET) refers to tim.o(i.TIM1_UP_IRQHandler) for TIM1_UP_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to tim.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to tim.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to tim.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to usart.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to usart.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f10x_md.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(HEAP) for Heap_Mem
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(STACK) for Stack_Mem
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for .data
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$********) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$********) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$********) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$********) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$********) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_f.o(.ARM.Collect$$_printf_percent$$********) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$********) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$********) for __rt_entry_sh
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(i.fputc) for fputc
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$********) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$********) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$********) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$********) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$********) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$********) for .ARM.Collect$$rtentry$$********
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_md.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$********) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$********) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$********) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$********) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$********) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$********) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    rtexit2.o(.ARM.Collect$$rtexit$$********) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$********) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$********) for .ARM.Collect$$rtexit$$********
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$********) for .ARM.Collect$$rtexit$$********
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$********) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to usart.o(i._ttywrch) for _ttywrch
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.bss), (256 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (1 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (1 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (100 bytes).
    Removing oled.o(i.Delay_1ms), (18 bytes).
    Removing oled.o(i.Delay_50ms), (20 bytes).
    Removing oled.o(i.IIC_Start1), (52 bytes).
    Removing oled.o(i.IIC_Stop1), (40 bytes).
    Removing oled.o(i.IIC_Wait_Ack1), (32 bytes).
    Removing oled.o(i.OLED_Clear), (58 bytes).
    Removing oled.o(i.OLED_Display), (72 bytes).
    Removing oled.o(i.OLED_Display_Off), (30 bytes).
    Removing oled.o(i.OLED_Display_On), (30 bytes).
    Removing oled.o(i.OLED_DrawBMP), (64 bytes).
    Removing oled.o(i.OLED_Init), (280 bytes).
    Removing oled.o(i.OLED_On), (58 bytes).
    Removing oled.o(i.OLED_SHOWCH), (152 bytes).
    Removing oled.o(i.OLED_SHOWCHSTR), (60 bytes).
    Removing oled.o(i.OLED_Set_Pixel), (36 bytes).
    Removing oled.o(i.OLED_Set_Pos), (42 bytes).
    Removing oled.o(i.OLED_ShowChar), (136 bytes).
    Removing oled.o(i.OLED_ShowNum), (106 bytes).
    Removing oled.o(i.OLED_ShowString), (54 bytes).
    Removing oled.o(i.OLED_WR_Byte), (12 bytes).
    Removing oled.o(i.Write_IIC_Byte), (88 bytes).
    Removing oled.o(i.Write_IIC_Command), (46 bytes).
    Removing oled.o(i.Write_IIC_Data), (46 bytes).
    Removing oled.o(i.fill_picture), (60 bytes).
    Removing oled.o(i.oled_pow), (16 bytes).
    Removing oled.o(.bss), (1024 bytes).
    Removing oled.o(.constdata), (4214 bytes).
    Removing oled.o(.data), (7080 bytes).
    Removing delay.o(i.delay_ms), (52 bytes).
    Removing delay.o(i.delay_us), (52 bytes).
    Removing sys.o(.emb_text), (6 bytes).
    Removing sys.o(i.INTX_DISABLE), (4 bytes).
    Removing sys.o(i.INTX_ENABLE), (4 bytes).
    Removing sys.o(i.WFI_SET), (4 bytes).
    Removing usart.o(i._ttywrch), (2 bytes).
    Removing usart.o(i.usart1_Rx_IRQ), (2 bytes).
    Removing usart.o(i.usart2_Rx_IRQ), (2 bytes).
    Removing adc.o(i.Get_Adc_Average), (46 bytes).
    Removing tim.o(i.tim1_init), (92 bytes).
    Removing tim.o(i.tim2_IRQ), (2 bytes).
    Removing tim.o(i.tim3_init), (92 bytes).
    Removing tim.o(i.tim4_IRQ), (2 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (22 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (180 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (40 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (28 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (88 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (6 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (14 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (6 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (14 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (64 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (44 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (6 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (116 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (14 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (16 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (108 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (368 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (50 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (14 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (300 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (128 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (128 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (100 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (108 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (70 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (46 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (74 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (28 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (20 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (32 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (112 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (14 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (56 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (56 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (124 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (60 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (40 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (40 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (80 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (136 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (96 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (36 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (36 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (22 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (228 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (6 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (32 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (32 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (16 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (58 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).

285 unused section(s) (total 21624 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ..\CORE\core_cm3.c                       0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CORE\startup_stm32f10x_md.s           0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\OLED_V1.0.0\oled.c           0x00000000   Number         0  oled.o ABSOLUTE
    ..\STM32F10x_FWLib\src\misc.c            0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_adc.c   0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_dma.c   0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_exti.c  0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_flash.c 0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_gpio.c  0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rcc.c   0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_tim.c   0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\SYSTEM\ADC\adc.c                      0x00000000   Number         0  adc.o ABSOLUTE
    ..\SYSTEM\TIM_V1.0.2\tim.c               0x00000000   Number         0  tim.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CORE\\core_cm3.c                     0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000160   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x08000160   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000166   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$********  0x0800016c   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$********)
    .ARM.Collect$$_printf_percent$$********  0x08000172   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$********)
    .ARM.Collect$$_printf_percent$$00000005  0x08000178   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$********  0x0800017e   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$********)
    .ARM.Collect$$_printf_percent$$00000007  0x08000184   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800018e   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$********  0x08000194   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$********)
    .ARM.Collect$$_printf_percent$$0000000A  0x0800019a   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x080001a0   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x080001a6   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x080001ac   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x080001b2   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x080001b8   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x080001be   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x080001c4   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x080001ca   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$********  0x080001d4   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$********)
    .ARM.Collect$$_printf_percent$$00000014  0x080001da   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x080001e0   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x080001e6   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x080001ec   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080001f0   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$********          0x080001f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$********)
    .ARM.Collect$$libinit$$0000000A          0x080001f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080001f2   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080001f8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x080001f8   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$********          0x08000204   Section        0  libinit2.o(.ARM.Collect$$libinit$$********)
    .ARM.Collect$$libinit$$00000015          0x08000204   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000204   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800020e   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000210   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000212   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$********      0x08000212   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$********)
    .ARM.Collect$$libshutdown$$00000007      0x08000212   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000212   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000212   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000212   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000212   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000214   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000214   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$********          0x08000214   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$********)
    .ARM.Collect$$rtentry$$********          0x0800021a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$********)
    .ARM.Collect$$rtentry$$0000000A          0x0800021a   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800021e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800021e   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000226   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000228   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$********           0x08000228   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$********)
    .ARM.Collect$$rtexit$$********           0x0800022c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$********)
    .text                                    0x08000234   Section       64  startup_stm32f10x_md.o(.text)
    .text                                    0x08000274   Section        2  use_no_semi_2.o(.text)
    .text                                    0x08000278   Section        0  vsnprintf.o(.text)
    .text                                    0x080002ac   Section        0  __2printf.o(.text)
    .text                                    0x080002c4   Section        0  __2sprintf.o(.text)
    .text                                    0x080002f0   Section        0  _printf_str.o(.text)
    .text                                    0x08000344   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x080004cc   Section        0  heapauxi.o(.text)
    .text                                    0x080004d2   Section        2  use_no_semi.o(.text)
    .text                                    0x080004d4   Section        0  _printf_pad.o(.text)
    .text                                    0x08000522   Section        0  _printf_truncate.o(.text)
    .text                                    0x08000548   Section        0  _printf_dec.o(.text)
    .text                                    0x080005c0   Section        0  _printf_charcount.o(.text)
    .text                                    0x080005e8   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x080005eb   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000a08   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000a09   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000a38   Section        0  _sputc.o(.text)
    .text                                    0x08000a42   Section        0  _snputc.o(.text)
    .text                                    0x08000a52   Section        0  _printf_char.o(.text)
    .text                                    0x08000a80   Section        0  _printf_char_file.o(.text)
    .text                                    0x08000aa4   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08000b60   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08000bdc   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x08000bdd   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000c4c   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08000c4d   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000ce0   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08000ce8   Section      138  lludiv10.o(.text)
    .text                                    0x08000d72   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000e24   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08001120   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x080011a0   Section        0  _printf_wchar.o(.text)
    .text                                    0x080011cc   Section        0  bigflt0.o(.text)
    .text                                    0x080012b0   Section        0  ferror.o(.text)
    .text                                    0x080012b8   Section        0  _wcrtomb.o(.text)
    .text                                    0x080012f8   Section        8  libspace.o(.text)
    .text                                    0x08001300   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800134c   Section       16  rt_ctype_table.o(.text)
    .text                                    0x0800135c   Section        0  exit.o(.text)
    .text                                    0x08001370   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x080013f0   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x0800142e   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08001474   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x080014d4   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x0800180c   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x080018e8   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08001912   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x0800193c   Section      580  btod.o(CL$$btod_mult_common)
    i.ADC_Cmd                                0x08001b80   Section        0  stm32f10x_adc.o(i.ADC_Cmd)
    i.ADC_DeInit                             0x08001b94   Section        0  stm32f10x_adc.o(i.ADC_DeInit)
    i.ADC_GetCalibrationStatus               0x08001bd8   Section        0  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    i.ADC_GetConversionValue                 0x08001be6   Section        0  stm32f10x_adc.o(i.ADC_GetConversionValue)
    i.ADC_GetFlagStatus                      0x08001bec   Section        0  stm32f10x_adc.o(i.ADC_GetFlagStatus)
    i.ADC_GetResetCalibrationStatus          0x08001bfa   Section        0  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    i.ADC_Init                               0x08001c08   Section        0  stm32f10x_adc.o(i.ADC_Init)
    i.ADC_RegularChannelConfig               0x08001c50   Section        0  stm32f10x_adc.o(i.ADC_RegularChannelConfig)
    i.ADC_ResetCalibration                   0x08001cc4   Section        0  stm32f10x_adc.o(i.ADC_ResetCalibration)
    i.ADC_SoftwareStartConvCmd               0x08001cce   Section        0  stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd)
    i.ADC_StartCalibration                   0x08001ce2   Section        0  stm32f10x_adc.o(i.ADC_StartCalibration)
    i.Adc_Init                               0x08001cec   Section        0  adc.o(i.Adc_Init)
    i.BusFault_Handler                       0x08001da0   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08001da2   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.Dis_get                                0x08001da4   Section        0  main.o(i.Dis_get)
    i.GPIO_Init                              0x08001dcc   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_SetBits                           0x08001e6e   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.Get_Adc                                0x08001e74   Section        0  adc.o(i.Get_Adc)
    i.LED_Init                               0x08001eb0   Section        0  led.o(i.LED_Init)
    i.Led                                    0x08001ef8   Section        0  main.o(i.Led)
    i.MemManage_Handler                      0x08001f04   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08001f06   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08001f08   Section        0  misc.o(i.NVIC_Init)
    i.PendSV_Handler                         0x08001f6c   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_ADCCLKConfig                       0x08001f70   Section        0  stm32f10x_rcc.o(i.RCC_ADCCLKConfig)
    i.RCC_APB1PeriphClockCmd                 0x08001f84   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08001f9c   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_APB2PeriphResetCmd                 0x08001fb4   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd)
    i.RCC_GetClocksFreq                      0x08001fcc   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x0800205c   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClockTo72                        0x08002060   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08002061   Thumb Code   160  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_CLKSourceConfig                0x08002108   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x08002120   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08002124   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM1_UP_IRQHandler                     0x08002174   Section        0  tim.o(i.TIM1_UP_IRQHandler)
    i.TIM2_IRQHandler                        0x08002198   Section        0  tim.o(i.TIM2_IRQHandler)
    i.TIM3_IRQHandler                        0x080021b8   Section        0  tim.o(i.TIM3_IRQHandler)
    i.TIM4_IRQHandler                        0x080021dc   Section        0  tim.o(i.TIM4_IRQHandler)
    i.TIM_ClearITPendingBit                  0x08002200   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x08002206   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_GetITStatus                        0x0800221a   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_ITConfig                           0x08002232   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_TimeBaseInit                       0x08002244   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.USART1_IRQHandler                      0x080022e0   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08002304   Section        0  usart.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08002328   Section        0  usart.o(i.USART3_IRQHandler)
    i.USART_Cmd                              0x0800234c   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetITStatus                      0x08002360   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x0800239e   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x080023d0   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x0800247c   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.USART_printf                           0x08002484   Section        0  usart.o(i.USART_printf)
    i.USART_sendBuf                          0x080024ae   Section        0  usart.o(i.USART_sendBuf)
    i.UsageFault_Handler                     0x080024c4   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x080024c6   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._is_digit                              0x080024ee   Section        0  __printf_wp.o(i._is_digit)
    i._sys_exit                              0x080024fc   Section        0  usart.o(i._sys_exit)
    i.delay_init                             0x08002500   Section        0  delay.o(i.delay_init)
    i.fputc                                  0x0800253c   Section        0  usart.o(i.fputc)
    i.main                                   0x08002554   Section        0  main.o(i.main)
    i.str_num                                0x08002608   Section        0  main.o(i.str_num)
    i.task_handler                           0x08002634   Section        0  main.o(i.task_handler)
    i.task_schedule_callback                 0x08002660   Section        0  main.o(i.task_schedule_callback)
    i.tim1_IRQ                               0x08002694   Section        0  tim.o(i.tim1_IRQ)
    i.tim2_IRQ                               0x08002698   Section        0  main.o(i.tim2_IRQ)
    i.tim2_init                              0x080026e0   Section        0  tim.o(i.tim2_init)
    i.tim3_IRQ                               0x0800273a   Section        0  tim.o(i.tim3_IRQ)
    i.tim4_IRQ                               0x0800273c   Section        0  main.o(i.tim4_IRQ)
    i.tim4_init                              0x08002740   Section        0  tim.o(i.tim4_init)
    i.uart1_init                             0x0800279c   Section        0  usart.o(i.uart1_init)
    i.uart2_init                             0x08002844   Section        0  usart.o(i.uart2_init)
    i.uart3_init                             0x080028e8   Section        0  usart.o(i.uart3_init)
    i.usart1_Rx_IRQ                          0x08002990   Section        0  main.o(i.usart1_Rx_IRQ)
    i.usart1_send                            0x080029f8   Section        0  main.o(i.usart1_send)
    i.usart2_Rx_IRQ                          0x08002c48   Section        0  main.o(i.usart2_Rx_IRQ)
    i.usart3_Rx_IRQ                          0x08002d94   Section        0  usart.o(i.usart3_Rx_IRQ)
    locale$$code                             0x08002d98   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x08002dc4   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$ddiv                               0x08002df0   Section      688  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08002df7   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dmul                               0x080030a0   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x080031f4   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08003290   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x0800329c   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fdiv                               0x080032f4   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x080032f5   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$fflt                               0x08003478   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$fnaninf                            0x080034a8   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08003534   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$printf1                            0x0800353e   Section        4  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x08003542   Section        4  printf2.o(x$fpl$printf2)
    .constdata                               0x08003546   Section       17  __printf_flags_ss_wp.o(.constdata)
    x$fpl$usenofp                            0x08003546   Section        0  usenofp.o(x$fpl$usenofp)
    maptable                                 0x08003546   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08003558   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x08003558   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x08003560   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x08003560   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x08003574   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x08003588   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x08003588   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x0800359b   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x080035b0   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x080035b0   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x080035ec   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x08003664   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08003668   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08003670   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0800367c   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800367e   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800367f   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x08003680   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x08003680   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x08003684   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x0800368c   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x08003790   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section       92  main.o(.data)
    g_tcb_tab                                0x20000044   Data          24  main.o(.data)
    .data                                    0x2000005c   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000070   Section        4  delay.o(.data)
    fac_us                                   0x20000070   Data           1  delay.o(.data)
    fac_ms                                   0x20000072   Data           2  delay.o(.data)
    .data                                    0x20000074   Section        4  usart.o(.data)
    .data                                    0x20000078   Section       20  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000078   Data           4  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x2000007c   Data          16  stm32f10x_rcc.o(.data)
    .bss                                     0x2000008c   Section      256  main.o(.bss)
    .bss                                     0x2000018c   Section       96  libspace.o(.bss)
    HEAP                                     0x200001f0   Section      512  startup_stm32f10x_md.o(HEAP)
    Heap_Mem                                 0x200001f0   Data         512  startup_stm32f10x_md.o(HEAP)
    STACK                                    0x200003f0   Section     1024  startup_stm32f10x_md.o(STACK)
    Stack_Mem                                0x200003f0   Data        1024  startup_stm32f10x_md.o(STACK)
    __initial_sp                             0x200007f0   Data           0  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x08000161   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x08000161   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000167   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x0800016d   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$********)
    _printf_e                                0x08000173   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$********)
    _printf_g                                0x08000179   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800017f   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$********)
    _printf_ll                               0x08000185   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800018f   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000195   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$********)
    _printf_u                                0x0800019b   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x080001a1   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x080001a7   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x080001ad   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x080001b3   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x080001b9   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x080001bf   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x080001c5   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x080001cb   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x080001d5   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$********)
    _printf_s                                0x080001db   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x080001e1   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x080001e7   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x080001ed   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080001f1   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x080001f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080001f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080001f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$********)
    __rt_lib_init_rand_1                     0x080001f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080001f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080001f9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x080001f9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x08000205   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$********)
    __rt_lib_init_lc_monetary_1              0x08000205   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000205   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000211   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$********)
    __rt_lib_shutdown_user_alloc_1           0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000215   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000215   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000215   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$********)
    __rt_entry_li                            0x0800021b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800021b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$********)
    __rt_entry_main                          0x0800021f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800021f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000227   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000229   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$********)
    __rt_exit_prels_1                        0x08000229   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800022d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$********)
    Reset_Handler                            0x08000235   Thumb Code     8  startup_stm32f10x_md.o(.text)
    HardFault_Handler                        0x0800023f   Thumb Code     2  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI0_IRQHandler                         0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x0800024f   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __user_initial_stackheap                 0x08000251   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __use_no_semihosting                     0x08000275   Thumb Code     2  use_no_semi_2.o(.text)
    vsnprintf                                0x08000279   Thumb Code    48  vsnprintf.o(.text)
    __2printf                                0x080002ad   Thumb Code    20  __2printf.o(.text)
    __2sprintf                               0x080002c5   Thumb Code    38  __2sprintf.o(.text)
    _printf_str                              0x080002f1   Thumb Code    82  _printf_str.o(.text)
    __printf                                 0x08000345   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __use_two_region_memory                  0x080004cd   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080004cf   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080004d1   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x080004d3   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080004d3   Thumb Code     2  use_no_semi.o(.text)
    _printf_pre_padding                      0x080004d5   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000501   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x08000523   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x08000535   Thumb Code    18  _printf_truncate.o(.text)
    _printf_int_dec                          0x08000549   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x080005c1   Thumb Code    40  _printf_charcount.o(.text)
    __lib_sel_fp_printf                      0x080005e9   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x0800079b   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000a13   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000a39   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x08000a43   Thumb Code    16  _snputc.o(.text)
    _printf_cs_common                        0x08000a53   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08000a67   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08000a77   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x08000a81   Thumb Code    32  _printf_char_file.o(.text)
    _printf_wctomb                           0x08000aa5   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x08000b61   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08000bdd   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08000c1f   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08000c37   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000c4d   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x08000ca3   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x08000cbf   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08000ccb   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __rt_locale                              0x08000ce1   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x08000ce9   Thumb Code   138  lludiv10.o(.text)
    _printf_int_common                       0x08000d73   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_fp_hex_real                      0x08000e25   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_fp_infnan                        0x08001121   Thumb Code   112  _printf_fp_infnan.o(.text)
    _printf_lcs_common                       0x080011a1   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x080011b5   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x080011c5   Thumb Code     8  _printf_wchar.o(.text)
    _btod_etento                             0x080011cd   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x080012b1   Thumb Code     8  ferror.o(.text)
    _wcrtomb                                 0x080012b9   Thumb Code    64  _wcrtomb.o(.text)
    __user_libspace                          0x080012f9   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080012f9   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080012f9   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08001301   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x0800134d   Thumb Code    16  rt_ctype_table.o(.text)
    exit                                     0x0800135d   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08001371   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x080013f1   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x0800142f   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08001475   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x080014d5   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x0800180d   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x080018e9   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08001913   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x0800193d   Thumb Code   580  btod.o(CL$$btod_mult_common)
    ADC_Cmd                                  0x08001b81   Thumb Code    20  stm32f10x_adc.o(i.ADC_Cmd)
    ADC_DeInit                               0x08001b95   Thumb Code    56  stm32f10x_adc.o(i.ADC_DeInit)
    ADC_GetCalibrationStatus                 0x08001bd9   Thumb Code    14  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    ADC_GetConversionValue                   0x08001be7   Thumb Code     6  stm32f10x_adc.o(i.ADC_GetConversionValue)
    ADC_GetFlagStatus                        0x08001bed   Thumb Code    14  stm32f10x_adc.o(i.ADC_GetFlagStatus)
    ADC_GetResetCalibrationStatus            0x08001bfb   Thumb Code    14  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    ADC_Init                                 0x08001c09   Thumb Code    62  stm32f10x_adc.o(i.ADC_Init)
    ADC_RegularChannelConfig                 0x08001c51   Thumb Code   116  stm32f10x_adc.o(i.ADC_RegularChannelConfig)
    ADC_ResetCalibration                     0x08001cc5   Thumb Code    10  stm32f10x_adc.o(i.ADC_ResetCalibration)
    ADC_SoftwareStartConvCmd                 0x08001ccf   Thumb Code    20  stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd)
    ADC_StartCalibration                     0x08001ce3   Thumb Code    10  stm32f10x_adc.o(i.ADC_StartCalibration)
    Adc_Init                                 0x08001ced   Thumb Code   170  adc.o(i.Adc_Init)
    BusFault_Handler                         0x08001da1   Thumb Code     2  stm32f10x_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08001da3   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    Dis_get                                  0x08001da5   Thumb Code    40  main.o(i.Dis_get)
    GPIO_Init                                0x08001dcd   Thumb Code   162  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_SetBits                             0x08001e6f   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    Get_Adc                                  0x08001e75   Thumb Code    56  adc.o(i.Get_Adc)
    LED_Init                                 0x08001eb1   Thumb Code    66  led.o(i.LED_Init)
    Led                                      0x08001ef9   Thumb Code     8  main.o(i.Led)
    MemManage_Handler                        0x08001f05   Thumb Code     2  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08001f07   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08001f09   Thumb Code    94  misc.o(i.NVIC_Init)
    PendSV_Handler                           0x08001f6d   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_ADCCLKConfig                         0x08001f71   Thumb Code    14  stm32f10x_rcc.o(i.RCC_ADCCLKConfig)
    RCC_APB1PeriphClockCmd                   0x08001f85   Thumb Code    18  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08001f9d   Thumb Code    18  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_APB2PeriphResetCmd                   0x08001fb5   Thumb Code    18  stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd)
    RCC_GetClocksFreq                        0x08001fcd   Thumb Code   128  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x0800205d   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SysTick_CLKSourceConfig                  0x08002109   Thumb Code    24  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x08002121   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x08002125   Thumb Code    64  system_stm32f10x.o(i.SystemInit)
    TIM1_UP_IRQHandler                       0x08002175   Thumb Code    32  tim.o(i.TIM1_UP_IRQHandler)
    TIM2_IRQHandler                          0x08002199   Thumb Code    32  tim.o(i.TIM2_IRQHandler)
    TIM3_IRQHandler                          0x080021b9   Thumb Code    32  tim.o(i.TIM3_IRQHandler)
    TIM4_IRQHandler                          0x080021dd   Thumb Code    32  tim.o(i.TIM4_IRQHandler)
    TIM_ClearITPendingBit                    0x08002201   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08002207   Thumb Code    20  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_GetITStatus                          0x0800221b   Thumb Code    24  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_ITConfig                             0x08002233   Thumb Code    16  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_TimeBaseInit                         0x08002245   Thumb Code   114  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    USART1_IRQHandler                        0x080022e1   Thumb Code    32  usart.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08002305   Thumb Code    32  usart.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08002329   Thumb Code    32  usart.o(i.USART3_IRQHandler)
    USART_Cmd                                0x0800234d   Thumb Code    20  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetITStatus                        0x08002361   Thumb Code    62  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x0800239f   Thumb Code    48  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x080023d1   Thumb Code   166  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x0800247d   Thumb Code     8  stm32f10x_usart.o(i.USART_ReceiveData)
    USART_printf                             0x08002485   Thumb Code    42  usart.o(i.USART_printf)
    USART_sendBuf                            0x080024af   Thumb Code    22  usart.o(i.USART_sendBuf)
    UsageFault_Handler                       0x080024c5   Thumb Code     2  stm32f10x_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x080024c7   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x080024ef   Thumb Code    14  __printf_wp.o(i._is_digit)
    _sys_exit                                0x080024fd   Thumb Code     2  usart.o(i._sys_exit)
    delay_init                               0x08002501   Thumb Code    46  delay.o(i.delay_init)
    fputc                                    0x0800253d   Thumb Code    18  usart.o(i.fputc)
    main                                     0x08002555   Thumb Code   110  main.o(i.main)
    str_num                                  0x08002609   Thumb Code    44  main.o(i.str_num)
    task_handler                             0x08002635   Thumb Code    40  main.o(i.task_handler)
    task_schedule_callback                   0x08002661   Thumb Code    48  main.o(i.task_schedule_callback)
    tim1_IRQ                                 0x08002695   Thumb Code     2  tim.o(i.tim1_IRQ)
    tim2_IRQ                                 0x08002699   Thumb Code    66  main.o(i.tim2_IRQ)
    tim2_init                                0x080026e1   Thumb Code    90  tim.o(i.tim2_init)
    tim3_IRQ                                 0x0800273b   Thumb Code     2  tim.o(i.tim3_IRQ)
    tim4_IRQ                                 0x0800273d   Thumb Code     4  main.o(i.tim4_IRQ)
    tim4_init                                0x08002741   Thumb Code    88  tim.o(i.tim4_init)
    uart1_init                               0x0800279d   Thumb Code   158  usart.o(i.uart1_init)
    uart2_init                               0x08002845   Thumb Code   154  usart.o(i.uart2_init)
    uart3_init                               0x080028e9   Thumb Code   158  usart.o(i.uart3_init)
    usart1_Rx_IRQ                            0x08002991   Thumb Code    64  main.o(i.usart1_Rx_IRQ)
    usart1_send                              0x080029f9   Thumb Code   368  main.o(i.usart1_send)
    usart2_Rx_IRQ                            0x08002c49   Thumb Code   296  main.o(i.usart2_Rx_IRQ)
    usart3_Rx_IRQ                            0x08002d95   Thumb Code     2  usart.o(i.usart3_Rx_IRQ)
    _get_lc_numeric                          0x08002d99   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x08002dc5   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_ddiv                             0x08002df1   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08002df1   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_dmul                             0x080030a1   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x080030a1   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x080031f5   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08003291   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x0800329d   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x0800329d   Thumb Code    86  f2d.o(x$fpl$f2d)
    __aeabi_fdiv                             0x080032f5   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x080032f5   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_i2f                              0x08003479   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x08003479   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __fpl_fnaninf                            0x080034a9   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08003535   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _printf_fp_dec                           0x0800353f   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x08003543   Thumb Code     4  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x08003546   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x08003644   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003664   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x0800368d   Data           0  lc_ctype_c.o(locale$$data)
    buff1                                    0x20000000   Data           1  main.o(.data)
    page                                     0x20000001   Data           1  main.o(.data)
    rx_flag                                  0x20000002   Data           1  main.o(.data)
    myShape                                  0x20000003   Data           1  main.o(.data)
    TTS_FLAG                                 0x20000004   Data           3  main.o(.data)
    Rx_index                                 0x20000007   Data           4  main.o(.data)
    ADC_SUM                                  0x2000000c   Data           4  main.o(.data)
    ADC_index                                0x20000010   Data           4  main.o(.data)
    ADC_COJ                                  0x20000014   Data           4  main.o(.data)
    adc_real                                 0x20000018   Data           4  main.o(.data)
    adcx1                                    0x2000001c   Data           4  main.o(.data)
    adcx2                                    0x20000020   Data           4  main.o(.data)
    dis_data                                 0x20000024   Data           4  main.o(.data)
    Q_data                                   0x20000028   Data           4  main.o(.data)
    Dis_buff                                 0x2000002c   Data           6  main.o(.data)
    R_buff                                   0x20000032   Data           6  main.o(.data)
    T_buff                                   0x20000038   Data           6  main.o(.data)
    Q_buff                                   0x2000003e   Data           6  main.o(.data)
    SystemCoreClock                          0x2000005c   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000060   Data          16  system_stm32f10x.o(.data)
    __stdout                                 0x20000074   Data           4  usart.o(.data)
    send_buff                                0x2000008c   Data         256  main.o(.bss)
    __libspace_start                         0x2000018c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200001ec   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000381c, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00003790, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO          769    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         2400  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x00000034   Code   RO         2724    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x08000128   0x0000001a   Code   RO         2726    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x08000142   0x00000002   PAD
    0x08000144   0x08000144   0x0000001c   Code   RO         2728    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x08000160   0x00000000   Code   RO         2397    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000160   0x08000160   0x********   Code   RO         2478    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000166   0x08000166   0x********   Code   RO         2480    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x0800016c   0x0800016c   0x********   Code   RO         2396    .ARM.Collect$$_printf_percent$$********  c_w.l(_printf_f.o)
    0x08000172   0x08000172   0x********   Code   RO         2485    .ARM.Collect$$_printf_percent$$********  c_w.l(_printf_e.o)
    0x08000178   0x08000178   0x********   Code   RO         2486    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800017e   0x0800017e   0x********   Code   RO         2487    .ARM.Collect$$_printf_percent$$********  c_w.l(_printf_a.o)
    0x08000184   0x08000184   0x0000000a   Code   RO         2492    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800018e   0x0800018e   0x********   Code   RO         2482    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000194   0x08000194   0x********   Code   RO         2483    .ARM.Collect$$_printf_percent$$********  c_w.l(_printf_d.o)
    0x0800019a   0x0800019a   0x********   Code   RO         2484    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x080001a0   0x080001a0   0x********   Code   RO         2481    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x080001a6   0x080001a6   0x********   Code   RO         2479    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x080001ac   0x080001ac   0x********   Code   RO         2489    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x080001b2   0x080001b2   0x********   Code   RO         2490    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x080001b8   0x080001b8   0x********   Code   RO         2491    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x080001be   0x080001be   0x********   Code   RO         2496    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x080001c4   0x080001c4   0x********   Code   RO         2497    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x080001ca   0x080001ca   0x0000000a   Code   RO         2493    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x080001d4   0x080001d4   0x********   Code   RO         2477    .ARM.Collect$$_printf_percent$$********  c_w.l(_printf_c.o)
    0x080001da   0x080001da   0x********   Code   RO         2395    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080001e0   0x080001e0   0x********   Code   RO         2494    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x080001e6   0x080001e6   0x********   Code   RO         2495    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x080001ec   0x080001ec   0x********   Code   RO         2488    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080001f0   0x080001f0   0x00000002   Code   RO         2600    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001f2   0x080001f2   0x00000000   Code   RO         2602    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001f2   0x080001f2   0x00000000   Code   RO         2604    .ARM.Collect$$libinit$$********  c_w.l(libinit2.o)
    0x080001f2   0x080001f2   0x00000000   Code   RO         2607    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001f2   0x080001f2   0x00000000   Code   RO         2609    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001f2   0x080001f2   0x00000000   Code   RO         2611    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001f2   0x080001f2   0x********   Code   RO         2612    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080001f8   0x080001f8   0x00000000   Code   RO         2614    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001f8   0x080001f8   0x0000000c   Code   RO         2615    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x08000204   0x08000204   0x00000000   Code   RO         2616    .ARM.Collect$$libinit$$********  c_w.l(libinit2.o)
    0x08000204   0x08000204   0x00000000   Code   RO         2618    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000204   0x08000204   0x0000000a   Code   RO         2619    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2620    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2622    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2624    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2626    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2628    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2630    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2632    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2634    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2638    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2640    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2642    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2644    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000002   Code   RO         2645    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000210   0x08000210   0x00000002   Code   RO         2676    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2685    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2687    .ARM.Collect$$libshutdown$$********  c_w.l(libshutdown2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2690    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2693    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2695    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2698    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000212   0x08000212   0x00000002   Code   RO         2699    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000214   0x08000214   0x00000000   Code   RO         2424    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000214   0x08000214   0x00000000   Code   RO         2514    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000214   0x08000214   0x********   Code   RO         2526    .ARM.Collect$$rtentry$$********  c_w.l(__rtentry4.o)
    0x0800021a   0x0800021a   0x00000000   Code   RO         2516    .ARM.Collect$$rtentry$$********  c_w.l(__rtentry2.o)
    0x0800021a   0x0800021a   0x********   Code   RO         2517    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         2519    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800021e   0x0800021e   0x00000008   Code   RO         2520    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000226   0x08000226   0x00000002   Code   RO         2646    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000228   0x08000228   0x00000000   Code   RO         2656    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000228   0x08000228   0x********   Code   RO         2657    .ARM.Collect$$rtexit$$********  c_w.l(rtexit2.o)
    0x0800022c   0x0800022c   0x********   Code   RO         2658    .ARM.Collect$$rtexit$$********  c_w.l(rtexit2.o)
    0x08000232   0x08000232   0x00000002   PAD
    0x08000234   0x08000234   0x00000040   Code   RO          770    .text               startup_stm32f10x_md.o
    0x08000274   0x08000274   0x00000002   Code   RO         2359    .text               c_w.l(use_no_semi_2.o)
    0x08000276   0x08000276   0x00000002   PAD
    0x08000278   0x08000278   0x00000034   Code   RO         2361    .text               c_w.l(vsnprintf.o)
    0x080002ac   0x080002ac   0x00000018   Code   RO         2363    .text               c_w.l(__2printf.o)
    0x080002c4   0x080002c4   0x0000002c   Code   RO         2365    .text               c_w.l(__2sprintf.o)
    0x080002f0   0x080002f0   0x00000052   Code   RO         2373    .text               c_w.l(_printf_str.o)
    0x08000342   0x08000342   0x00000002   PAD
    0x08000344   0x08000344   0x00000188   Code   RO         2392    .text               c_w.l(__printf_flags_ss_wp.o)
    0x080004cc   0x080004cc   0x********   Code   RO         2398    .text               c_w.l(heapauxi.o)
    0x080004d2   0x080004d2   0x00000002   Code   RO         2422    .text               c_w.l(use_no_semi.o)
    0x080004d4   0x080004d4   0x0000004e   Code   RO         2425    .text               c_w.l(_printf_pad.o)
    0x08000522   0x08000522   0x00000024   Code   RO         2427    .text               c_w.l(_printf_truncate.o)
    0x08000546   0x08000546   0x00000002   PAD
    0x08000548   0x08000548   0x00000078   Code   RO         2429    .text               c_w.l(_printf_dec.o)
    0x080005c0   0x080005c0   0x00000028   Code   RO         2431    .text               c_w.l(_printf_charcount.o)
    0x080005e8   0x080005e8   0x0000041e   Code   RO         2433    .text               c_w.l(_printf_fp_dec.o)
    0x08000a06   0x08000a06   0x00000002   PAD
    0x08000a08   0x08000a08   0x00000030   Code   RO         2435    .text               c_w.l(_printf_char_common.o)
    0x08000a38   0x08000a38   0x0000000a   Code   RO         2437    .text               c_w.l(_sputc.o)
    0x08000a42   0x08000a42   0x00000010   Code   RO         2439    .text               c_w.l(_snputc.o)
    0x08000a52   0x08000a52   0x0000002c   Code   RO         2441    .text               c_w.l(_printf_char.o)
    0x08000a7e   0x08000a7e   0x00000002   PAD
    0x08000a80   0x08000a80   0x00000024   Code   RO         2443    .text               c_w.l(_printf_char_file.o)
    0x08000aa4   0x08000aa4   0x000000bc   Code   RO         2445    .text               c_w.l(_printf_wctomb.o)
    0x08000b60   0x08000b60   0x0000007c   Code   RO         2448    .text               c_w.l(_printf_longlong_dec.o)
    0x08000bdc   0x08000bdc   0x00000070   Code   RO         2454    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000c4c   0x08000c4c   0x00000094   Code   RO         2474    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x08000ce0   0x08000ce0   0x00000008   Code   RO         2531    .text               c_w.l(rt_locale_intlibspace.o)
    0x08000ce8   0x08000ce8   0x0000008a   Code   RO         2533    .text               c_w.l(lludiv10.o)
    0x08000d72   0x08000d72   0x000000b2   Code   RO         2535    .text               c_w.l(_printf_intcommon.o)
    0x08000e24   0x08000e24   0x000002fc   Code   RO         2537    .text               c_w.l(_printf_fp_hex.o)
    0x08001120   0x08001120   0x00000080   Code   RO         2540    .text               c_w.l(_printf_fp_infnan.o)
    0x080011a0   0x080011a0   0x0000002c   Code   RO         2544    .text               c_w.l(_printf_wchar.o)
    0x080011cc   0x080011cc   0x000000e4   Code   RO         2546    .text               c_w.l(bigflt0.o)
    0x080012b0   0x080012b0   0x00000008   Code   RO         2571    .text               c_w.l(ferror.o)
    0x080012b8   0x080012b8   0x00000040   Code   RO         2575    .text               c_w.l(_wcrtomb.o)
    0x080012f8   0x080012f8   0x00000008   Code   RO         2584    .text               c_w.l(libspace.o)
    0x08001300   0x08001300   0x0000004a   Code   RO         2587    .text               c_w.l(sys_stackheap_outer.o)
    0x0800134a   0x0800134a   0x00000002   PAD
    0x0800134c   0x0800134c   0x00000010   Code   RO         2589    .text               c_w.l(rt_ctype_table.o)
    0x0800135c   0x0800135c   0x00000012   Code   RO         2591    .text               c_w.l(exit.o)
    0x0800136e   0x0800136e   0x00000002   PAD
    0x08001370   0x08001370   0x00000080   Code   RO         2593    .text               c_w.l(strcmpv7m.o)
    0x080013f0   0x080013f0   0x0000003e   Code   RO         2549    CL$$btod_d2e        c_w.l(btod.o)
    0x0800142e   0x0800142e   0x00000046   Code   RO         2551    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08001474   0x08001474   0x00000060   Code   RO         2550    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x080014d4   0x080014d4   0x00000338   Code   RO         2559    CL$$btod_div_common  c_w.l(btod.o)
    0x0800180c   0x0800180c   0x000000dc   Code   RO         2556    CL$$btod_e2e        c_w.l(btod.o)
    0x080018e8   0x080018e8   0x0000002a   Code   RO         2553    CL$$btod_ediv       c_w.l(btod.o)
    0x08001912   0x08001912   0x0000002a   Code   RO         2552    CL$$btod_emul       c_w.l(btod.o)
    0x0800193c   0x0800193c   0x00000244   Code   RO         2558    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001b80   0x08001b80   0x00000014   Code   RO         1843    i.ADC_Cmd           stm32f10x_adc.o
    0x08001b94   0x08001b94   0x00000044   Code   RO         1845    i.ADC_DeInit        stm32f10x_adc.o
    0x08001bd8   0x08001bd8   0x0000000e   Code   RO         1851    i.ADC_GetCalibrationStatus  stm32f10x_adc.o
    0x08001be6   0x08001be6   0x********   Code   RO         1852    i.ADC_GetConversionValue  stm32f10x_adc.o
    0x08001bec   0x08001bec   0x0000000e   Code   RO         1854    i.ADC_GetFlagStatus  stm32f10x_adc.o
    0x08001bfa   0x08001bfa   0x0000000e   Code   RO         1857    i.ADC_GetResetCalibrationStatus  stm32f10x_adc.o
    0x08001c08   0x08001c08   0x00000048   Code   RO         1861    i.ADC_Init          stm32f10x_adc.o
    0x08001c50   0x08001c50   0x00000074   Code   RO         1865    i.ADC_RegularChannelConfig  stm32f10x_adc.o
    0x08001cc4   0x08001cc4   0x0000000a   Code   RO         1866    i.ADC_ResetCalibration  stm32f10x_adc.o
    0x08001cce   0x08001cce   0x00000014   Code   RO         1868    i.ADC_SoftwareStartConvCmd  stm32f10x_adc.o
    0x08001ce2   0x08001ce2   0x0000000a   Code   RO         1870    i.ADC_StartCalibration  stm32f10x_adc.o
    0x08001cec   0x08001cec   0x000000b4   Code   RO          655    i.Adc_Init          adc.o
    0x08001da0   0x08001da0   0x00000002   Code   RO          211    i.BusFault_Handler  stm32f10x_it.o
    0x08001da2   0x08001da2   0x00000002   Code   RO          212    i.DebugMon_Handler  stm32f10x_it.o
    0x08001da4   0x08001da4   0x00000028   Code   RO            1    i.Dis_get           main.o
    0x08001dcc   0x08001dcc   0x000000a2   Code   RO          780    i.GPIO_Init         stm32f10x_gpio.o
    0x08001e6e   0x08001e6e   0x********   Code   RO          788    i.GPIO_SetBits      stm32f10x_gpio.o
    0x08001e72   0x08001e72   0x00000002   PAD
    0x08001e74   0x08001e74   0x0000003c   Code   RO          656    i.Get_Adc           adc.o
    0x08001eb0   0x08001eb0   0x00000048   Code   RO          323    i.LED_Init          led.o
    0x08001ef8   0x08001ef8   0x0000000c   Code   RO            2    i.Led               main.o
    0x08001f04   0x08001f04   0x00000002   Code   RO          213    i.MemManage_Handler  stm32f10x_it.o
    0x08001f06   0x08001f06   0x00000002   Code   RO          214    i.NMI_Handler       stm32f10x_it.o
    0x08001f08   0x08001f08   0x00000064   Code   RO         1268    i.NVIC_Init         misc.o
    0x08001f6c   0x08001f6c   0x00000002   Code   RO          215    i.PendSV_Handler    stm32f10x_it.o
    0x08001f6e   0x08001f6e   0x00000002   PAD
    0x08001f70   0x08001f70   0x00000014   Code   RO          888    i.RCC_ADCCLKConfig  stm32f10x_rcc.o
    0x08001f84   0x08001f84   0x00000018   Code   RO          890    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08001f9c   0x08001f9c   0x00000018   Code   RO          892    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08001fb4   0x08001fb4   0x00000018   Code   RO          893    i.RCC_APB2PeriphResetCmd  stm32f10x_rcc.o
    0x08001fcc   0x08001fcc   0x00000090   Code   RO          900    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x0800205c   0x0800205c   0x00000002   Code   RO          216    i.SVC_Handler       stm32f10x_it.o
    0x0800205e   0x0800205e   0x00000002   PAD
    0x08002060   0x08002060   0x000000a8   Code   RO          294    i.SetSysClockTo72   system_stm32f10x.o
    0x08002108   0x08002108   0x00000018   Code   RO         1272    i.SysTick_CLKSourceConfig  misc.o
    0x08002120   0x08002120   0x00000002   Code   RO          217    i.SysTick_Handler   stm32f10x_it.o
    0x08002122   0x08002122   0x00000002   PAD
    0x08002124   0x08002124   0x00000050   Code   RO          296    i.SystemInit        system_stm32f10x.o
    0x08002174   0x08002174   0x00000024   Code   RO          679    i.TIM1_UP_IRQHandler  tim.o
    0x08002198   0x08002198   0x00000020   Code   RO          680    i.TIM2_IRQHandler   tim.o
    0x080021b8   0x080021b8   0x00000024   Code   RO          681    i.TIM3_IRQHandler   tim.o
    0x080021dc   0x080021dc   0x00000024   Code   RO          682    i.TIM4_IRQHandler   tim.o
    0x08002200   0x08002200   0x********   Code   RO         1313    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x08002206   0x08002206   0x00000014   Code   RO         1318    i.TIM_Cmd           stm32f10x_tim.o
    0x0800221a   0x0800221a   0x00000018   Code   RO         1339    i.TIM_GetITStatus   stm32f10x_tim.o
    0x08002232   0x08002232   0x00000010   Code   RO         1343    i.TIM_ITConfig      stm32f10x_tim.o
    0x08002242   0x08002242   0x00000002   PAD
    0x08002244   0x08002244   0x0000009c   Code   RO         1389    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x080022e0   0x080022e0   0x00000024   Code   RO          552    i.USART1_IRQHandler  usart.o
    0x08002304   0x08002304   0x00000024   Code   RO          553    i.USART2_IRQHandler  usart.o
    0x08002328   0x08002328   0x00000024   Code   RO          554    i.USART3_IRQHandler  usart.o
    0x0800234c   0x0800234c   0x00000014   Code   RO         1092    i.USART_Cmd         stm32f10x_usart.o
    0x08002360   0x08002360   0x0000003e   Code   RO         1096    i.USART_GetITStatus  stm32f10x_usart.o
    0x0800239e   0x0800239e   0x00000030   Code   RO         1098    i.USART_ITConfig    stm32f10x_usart.o
    0x080023ce   0x080023ce   0x00000002   PAD
    0x080023d0   0x080023d0   0x000000ac   Code   RO         1099    i.USART_Init        stm32f10x_usart.o
    0x0800247c   0x0800247c   0x00000008   Code   RO         1106    i.USART_ReceiveData  stm32f10x_usart.o
    0x08002484   0x08002484   0x0000002a   Code   RO          555    i.USART_printf      usart.o
    0x080024ae   0x080024ae   0x00000016   Code   RO          556    i.USART_sendBuf     usart.o
    0x080024c4   0x080024c4   0x00000002   Code   RO          218    i.UsageFault_Handler  stm32f10x_it.o
    0x080024c6   0x080024c6   0x00000028   Code   RO         2582    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x080024ee   0x080024ee   0x0000000e   Code   RO         2385    i._is_digit         c_w.l(__printf_wp.o)
    0x080024fc   0x080024fc   0x00000002   Code   RO          557    i._sys_exit         usart.o
    0x080024fe   0x080024fe   0x00000002   PAD
    0x08002500   0x08002500   0x0000003c   Code   RO          497    i.delay_init        delay.o
    0x0800253c   0x0800253c   0x00000018   Code   RO          559    i.fputc             usart.o
    0x08002554   0x08002554   0x000000b4   Code   RO            3    i.main              main.o
    0x08002608   0x08002608   0x0000002c   Code   RO            4    i.str_num           main.o
    0x08002634   0x08002634   0x0000002c   Code   RO            5    i.task_handler      main.o
    0x08002660   0x08002660   0x00000034   Code   RO            6    i.task_schedule_callback  main.o
    0x08002694   0x08002694   0x00000002   Code   RO          683    i.tim1_IRQ          tim.o
    0x08002696   0x08002696   0x00000002   PAD
    0x08002698   0x08002698   0x00000048   Code   RO            7    i.tim2_IRQ          main.o
    0x080026e0   0x080026e0   0x0000005a   Code   RO          686    i.tim2_init         tim.o
    0x0800273a   0x0800273a   0x00000002   Code   RO          687    i.tim3_IRQ          tim.o
    0x0800273c   0x0800273c   0x********   Code   RO            8    i.tim4_IRQ          main.o
    0x08002740   0x08002740   0x0000005c   Code   RO          690    i.tim4_init         tim.o
    0x0800279c   0x0800279c   0x000000a8   Code   RO          560    i.uart1_init        usart.o
    0x08002844   0x08002844   0x000000a4   Code   RO          561    i.uart2_init        usart.o
    0x080028e8   0x080028e8   0x000000a8   Code   RO          562    i.uart3_init        usart.o
    0x08002990   0x08002990   0x00000068   Code   RO            9    i.usart1_Rx_IRQ     main.o
    0x080029f8   0x080029f8   0x00000250   Code   RO           10    i.usart1_send       main.o
    0x08002c48   0x08002c48   0x0000014c   Code   RO           11    i.usart2_Rx_IRQ     main.o
    0x08002d94   0x08002d94   0x00000002   Code   RO          565    i.usart3_Rx_IRQ     usart.o
    0x08002d96   0x08002d96   0x00000002   PAD
    0x08002d98   0x08002d98   0x0000002c   Code   RO         2578    locale$$code        c_w.l(lc_numeric_c.o)
    0x08002dc4   0x08002dc4   0x0000002c   Code   RO         2649    locale$$code        c_w.l(lc_ctype_c.o)
    0x08002df0   0x08002df0   0x000002b0   Code   RO         2403    x$fpl$ddiv          fz_ws.l(ddiv.o)
    0x080030a0   0x080030a0   0x00000154   Code   RO         2406    x$fpl$dmul          fz_ws.l(dmul.o)
    0x080031f4   0x080031f4   0x0000009c   Code   RO         2498    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x08003290   0x08003290   0x0000000c   Code   RO         2500    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x0800329c   0x0800329c   0x00000056   Code   RO         2408    x$fpl$f2d           fz_ws.l(f2d.o)
    0x080032f2   0x080032f2   0x00000002   PAD
    0x080032f4   0x080032f4   0x00000184   Code   RO         2411    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x08003478   0x08003478   0x00000030   Code   RO         2415    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x080034a8   0x080034a8   0x0000008c   Code   RO         2502    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08003534   0x08003534   0x0000000a   Code   RO         2504    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x0800353e   0x0800353e   0x********   Code   RO         2420    x$fpl$printf1       fz_ws.l(printf1.o)
    0x08003542   0x08003542   0x********   Code   RO         2506    x$fpl$printf2       fz_ws.l(printf2.o)
    0x08003546   0x08003546   0x00000000   Code   RO         2512    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08003546   0x08003546   0x00000011   Data   RO         2393    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08003557   0x08003557   0x00000001   PAD
    0x08003558   0x08003558   0x00000008   Data   RO         2446    .constdata          c_w.l(_printf_wctomb.o)
    0x08003560   0x08003560   0x00000028   Data   RO         2475    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x08003588   0x08003588   0x00000026   Data   RO         2538    .constdata          c_w.l(_printf_fp_hex.o)
    0x080035ae   0x080035ae   0x00000002   PAD
    0x080035b0   0x080035b0   0x00000094   Data   RO         2547    .constdata          c_w.l(bigflt0.o)
    0x08003644   0x08003644   0x00000020   Data   RO         2722    Region$$Table       anon$$obj.o
    0x08003664   0x08003664   0x0000001c   Data   RO         2577    locale$$data        c_w.l(lc_numeric_c.o)
    0x08003680   0x08003680   0x00000110   Data   RO         2648    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003790, Size: 0x000007f0, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08003790   0x0000005c   Data   RW           14    .data               main.o
    0x2000005c   0x080037ec   0x00000014   Data   RW          297    .data               system_stm32f10x.o
    0x20000070   0x08003800   0x********   Data   RW          500    .data               delay.o
    0x20000074   0x08003804   0x********   Data   RW          566    .data               usart.o
    0x20000078   0x08003808   0x00000014   Data   RW          920    .data               stm32f10x_rcc.o
    0x2000008c        -       0x00000100   Zero   RW           13    .bss                main.o
    0x2000018c        -       0x00000060   Zero   RW         2585    .bss                c_w.l(libspace.o)
    0x200001ec   0x0800381c   0x********   PAD
    0x200001f0        -       0x00000200   Zero   RW          768    HEAP                startup_stm32f10x_md.o
    0x200003f0        -       0x00000400   Zero   RW          767    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       240         14          0          0          0       1181   adc.o
        60         14          0          4          0        641   delay.o
        72          6          0          0          0        483   led.o
      1476        392          0         92        256     268063   main.o
       124          6          0          0          0       1474   misc.o
        64         26        236          0       1536        784   startup_stm32f10x_md.o
       364         22          0          0          0       8438   stm32f10x_adc.o
       166          0          0          0          0       2347   stm32f10x_gpio.o
        16          0          0          0          0       3126   stm32f10x_it.o
       236         40          0         20          0       5470   stm32f10x_rcc.o
       222         42          0          0          0       4349   stm32f10x_tim.o
       310          6          0          0          0       4922   stm32f10x_usart.o
         0          0          0          0          0         32   sys.o
       248         24          0         20          0       1649   system_stm32f10x.o
       326         16          0          0          0       3977   tim.o
       700         48          0          4          0       8336   usart.o

    ----------------------------------------------------------------------
      4642        <USER>        <GROUP>        140       1792     315272   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        18          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        24          4          0          0          0         84   __2printf.o
        44          6          0          0          0         84   __2sprintf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        30          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        52          4          0          0          0         80   vsnprintf.o
       688        140          0          0          0        208   ddiv.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
        86          4          0          0          0         84   f2d.o
       388         76          0          0          0         96   fdiv.o
        48          0          0          0          0         68   fflt_clz.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
         4          0          0          0          0         68   printf1.o
         4          0          0          0          0         68   printf2.o
         0          0          0          0          0          0   usenofp.o
        40          0          0          0          0         68   fpclassify.o

    ----------------------------------------------------------------------
      8760        <USER>        <GROUP>          0        100       5356   Library Totals
        20          0          3          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      6824        276        551          0         96       4280   c_w.l
      1876        240          0          0          0       1008   fz_ws.l
        40          0          0          0          0         68   m_ws.l

    ----------------------------------------------------------------------
      8760        <USER>        <GROUP>          0        100       5356   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     13402       1172        822        140       1892     314072   Grand Totals
     13402       1172        822        140       1892     314072   ELF Image Totals
     13402       1172        822        140          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                14224 (  13.89kB)
    Total RW  Size (RW Data + ZI Data)              2032 (   1.98kB)
    Total ROM Size (Code + RO Data + RW Data)      14364 (  14.03kB)

==============================================================================

