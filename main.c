#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "sys.h"
#include "adc.h"
#include "tim.h"
#include "usart.h"
#include "delay.h"

#include "led.h"
#include "oled.h"
#define TASK_NUM_MAX   (sizeof(g_tcb_tab) / sizeof(g_tcb_tab[0]))

#define  current_aj       0.33690              //��������ϵ��  ����ֵ���Ը������
int str_num(uint8_t *buff);
int Dis_get(uint8_t *buff);
uint8_t Rx_buff[256];    // ���ջ�����
uint8_t Rx_index[4];        // ���ջ���������
uint32_t ADC_SUM;        // ADC�ɼ�����ֵ  ���ھ�ֵ�˲�
int      ADC_index;      // ADC�ɼ�����    ���ھ�ֵ�˲�
int      ADC_COJ;        
int      adc_real;        // ��ֵ�˲����ADCֵ
int adcx1,adcx2,adcx3,adcx4;  			//ADת���Ľ��
float adc1,adc2,adc3,adc4;
uint8_t stay_flag;
float adc_out;    //����ֵ
void usart1_send();
void Led();
char send_buff[256];
char Dis_buff[6];
char <PERSON>_buff[6];
char T_buff[6];
char Q_buff[6];
u8 buff1 = 0;
uint8_t TTS_FLAG[3];
float dis_data;
float R_data;
float T_data;
float Q_data;
typedef struct
{
	uint8_t run;                       // ���ȱ�־��1�����ȣ�0������
	uint16_t time_count;               // ʱ��Ƭ����ֵ
	uint16_t time_reload;              // ʱ��Ƭ����ֵ
	void (*p_task_callback)(void);     // ����ָ���������������ҵ����ģ�麯����ַ
} st_tcb_t;

static st_tcb_t g_tcb_tab[] = 
{
  {0,500,500,Led},
  {0,200,200,usart1_send},
	/* ����ҵ����ģ�� */
};
/*******������*******/
int i;

typedef  enum{
	 D_flag,
	 Q_flag,
	 R_flag,
	 T_flag,
	 mnot,
}Rx_flag;

typedef enum {
    SHAPE_RECTANGLE,    // ����
    SHAPE_TRIANGLE,     // ������
    SHAPE_CIRCLE        // Բ��
} Shape_Type;

enum {
	jichu   = 0xE1,
	fahui   = 0xE2,
	xuanzhan= 0xE3,
	zuixiao = 0xE4,
	basic,
};

uint8_t page = basic;
Shape_Type myShape;

Rx_flag rx_flag  = mnot;
void Led(){
	buff1 = 0;
}

/***********************************************************
* @brief    �ж�������ȱ�־����ִ�б����ȵ�������
* @param    none
* @return   none 
**********************************************************/
void task_handler(void)
{
	for (uint8_t i = 0; i < TASK_NUM_MAX; i++)
	{
		if (g_tcb_tab[i].run)                  // �ж�ʱ��Ƭ��־
		{
			g_tcb_tab[i].run = 0;              // ��־����
			g_tcb_tab[i].p_task_callback();    // ִ�е���ҵ����ģ��
		}
	}
}

/**
***********************************************************
* @brief    �ڶ�ʱ���жϷ������б���ӵ��ã�����ʱ��Ƭ��ǣ�
            ��Ҫ��ʱ��1ms����1���ж�
* @param    none
* @return   none 
***********************************************************
*/
void task_schedule_callback(void)
{
	for (uint8_t i = 0; i < TASK_NUM_MAX; i++)
	{
		if (g_tcb_tab[i].time_count)
		{
			g_tcb_tab[i].time_count--;
			if (g_tcb_tab[i].time_count == 0)
			{
				g_tcb_tab[i].run = 1;
				g_tcb_tab[i].time_count = g_tcb_tab[i].time_reload;
			}
		}
	}
}

void  usart1_send(){
    switch(page){
		case jichu:
			sprintf(send_buff,"%.1f CM",dis_data);
            printf("t2.txt=\"%s \"\xFF\xFF\xFF",send_buff);
		    sprintf(send_buff,"%.1f CM",Q_data);
            printf("t4.txt=\"%s\"\xFF\xFF\xFF",send_buff);
			if(rx_flag == R_flag){
			sprintf(send_buff,"SHAPE_CIRCLE");
			printf("t6.txt=\"%s\"\xFF\xFF\xFF",send_buff);
			}
			else if(rx_flag == T_flag){
			sprintf(send_buff,"SHAPE_TRIANGLE");
			printf("t6.txt=\"%s\"\xFF\xFF\xFF",send_buff);
			}
			else if(rx_flag == Q_flag){
			sprintf(send_buff,"SHAPE_RECTANGLE");
			printf("t6.txt=\"%s\"\xFF\xFF\xFF",send_buff);
			}
			sprintf(send_buff,"%.1f W",(float)adc_real*current_aj*5/1000);
			printf("t6.txt=\"%s\"\xFF\xFF\xFF",send_buff);
			sprintf(send_buff,"%.2f mA",(float)adc_real*current_aj);
			printf("t8.txt=\"%s\"\xFF\xFF\xFF",send_buff);
			break;
		case zuixiao:
			sprintf(send_buff,"%.1f CM",dis_data);
            printf("t10.txt=\"%s\"\xFF\xFF\xFF",send_buff);
		    sprintf(send_buff,"%.1f CM",Q_data);
            printf("t11.txt=\"%s\"\xFF\xFF\xFF",send_buff);
			sprintf(send_buff,"%.1f W",(float)adc_real*current_aj*5/1000);
			printf("t12.txt=\"%s\"\xFF\xFF\xFF",send_buff);
			sprintf(send_buff,"%.2f mA",(float)adc_real*current_aj);
			printf("t13.txt=\"%s\"\xFF\xFF\xFF",send_buff);
		    break;
		case xuanzhan:
			sprintf(send_buff,"%.1f CM",dis_data);
            printf("t10.txt=\"%s\"\xFF\xFF\xFF",send_buff);
		    sprintf(send_buff,"%.1f CM",Q_data);
            printf("t11.txt=\"%s\"\xFF\xFF\xFF",send_buff);
			sprintf(send_buff,"%.1f W",(float)adc_real*current_aj*5/1000);
			printf("t12.txt=\"%s\"\xFF\xFF\xFF",send_buff);
			sprintf(send_buff,"%.2f mA",(float)adc_real*current_aj);
			printf("t13.txt=\"%s\"\xFF\xFF\xFF",send_buff);
		    break;
		case basic:

		    break;
		
	}
//	if(stay_flag == 1){
//	sprintf(send_buff,"%.1f CM",dis_data);
//  printf("t2.txt=\"%s\"\xFF\xFF\xFF",send_buff);
//	sprintf(send_buff,"%.1f mm",Q_data);
//    printf("t4.txt=\"%s\"\xFF\xFF\xFF",send_buff);
//	}
//	if(rx_flag == R_flag){
//    sprintf(send_buff,"SHAPE_CIRCLE");
//    printf("t6.txt=\"%s\"\xFF\xFF\xFF",send_buff);
//	}
//	else if(rx_flag == T_flag){
//    sprintf(send_buff,"SHAPE_TRIANGLE");
//    printf("t6.txt=\"%s\"\xFF\xFF\xFF",send_buff);
//	}
//	else if(rx_flag == Q_flag){
//    sprintf(send_buff,"SHAPE_RECTANGLE");
//    printf("t6.txt=\"%s\"\xFF\xFF\xFF",send_buff);
//	}
//    sprintf(send_buff,"%.1f W",(float)adc_real*current_aj*5/1000);
//    printf("t6.txt=\"%s\"\xFF\xFF\xFF",send_buff);
//	sprintf(send_buff,"%.2f mA",(float)adc_real*current_aj);
//    printf("t8.txt=\"%s\"\xFF\xFF\xFF",send_buff);
}

/**
 * @brief 	  ����1�����жϷ�����
 * 
 */
u8 buff;
int aa;
void usart1_Rx_IRQ(uint8_t c)
{
	switch(c)
	{
		case 0xC1 : 
		page = jichu;
		break;
		case 0xC2 : 
		page = fahui;
		break;
		case 0xA1 : 
		page = zuixiao;
    	USART_printf(USART2,"detectminQuad");
		break;
		case 0xA2 : 
		page = xuanzhan;
		USART_printf(USART2,"tilt");
		break;
		case 0xC5 : 
		page = basic;
		USART_printf(USART2,"basic");
		break;
	    default:
		break;
	}
}
void usart2_Rx_IRQ(uint8_t c)
{
	switch(c)
	{
	case 'D':
		rx_flag = D_flag;
		break;
	case 'R':
		rx_flag = R_flag;
		myShape = SHAPE_CIRCLE;
		break;
	case 'T':
		rx_flag = T_flag;
		myShape = SHAPE_TRIANGLE;
		break;
	case 'Q':
		rx_flag = Q_flag;
		myShape = SHAPE_RECTANGLE;
		break;
	}
	switch(rx_flag)
	{
	case D_flag:
	    Dis_buff[Rx_index[0]++] = c;
		if(c == '\n')
		{   
			dis_data = Dis_get(Dis_buff);
			dis_data = dis_data/10.0;
			rx_flag = mnot;
			Rx_index[0] = 0;
		}
		break;
	case Q_flag:
		
	    if(TTS_FLAG[0] == 0){
		TTS_FLAG[0] = 1;
	    TTS_FLAG[1] = 0;
		TTS_FLAG[2] = 0;
		USART_printf(USART3,"����");
		}
	    Q_buff[Rx_index[1]++] = c;
		if(c == '\n')
		{   
			Q_data = str_num(Q_buff);
			Q_data = Q_data/10.0;
			rx_flag = mnot;
			Rx_index[1] = 0;
		}
		break;
	case R_flag:
	    if(TTS_FLAG[1] == 0){
		TTS_FLAG[0] = 0;
	    TTS_FLAG[1] = 1;
		TTS_FLAG[2] = 0;
	    USART_printf(USART3,"Բ��");
		}
	    R_buff[Rx_index[2]++] = c;
		if(c == '\n')
		{   
            Q_data = str_num(R_buff);
			rx_flag = mnot;
			Rx_index[2] = 0;
		}
		break;
	case T_flag:
	    if(TTS_FLAG[2] == 0){
		TTS_FLAG[0] = 0;
	    TTS_FLAG[1] = 0;
		TTS_FLAG[2] = 1;
	    USART_printf(USART3,"������");
		}
	    T_buff[Rx_index[3]++] = c;
		if(c == '\n')
		{   
            Q_data = str_num(T_buff);
			rx_flag = mnot;
			Rx_index[3] = 0;
		}
		break;
	case mnot:
		break;
	}
	
}
int main(void)
{	
	delay_init();	   		// ��ʱ������ʼ��	  
	LED_Init();		  		// ��ʼ�����������ӵ�Ӳ���ӿ�
	uart1_init(9600);		// ���ڳ�ʼ
	uart3_init(9600);		// ���ڳ�ʼ
	uart2_init(115200);		// ���ڳ�ʼ��
	Adc_Init();							//ADCת����ʼ��
	tim2_init(100-1,72-1);	// ��ʱ����ʼ��
	tim4_init(1000-1,72-1);	// ��ʱ����ʼ��
	sprintf(send_buff,"%.1f CM",0.00);
    printf("t2.txt=\"%s\"\xFF\xFF\xFF",send_buff);
	sprintf(send_buff,"%.1f CM",0.00);
    printf("t4.txt=\"%s\"\xFF\xFF\xFF",send_buff);
    printf("t5.txt=\"%s\"\xFF\xFF\xFF","����:");
//	USART_printf(USART2,"detectminQuad");
//    USART_printf(USART2,"tilt");
	while(1)
	{
    task_handler();
	}
}
	void tim4_IRQ(void)			//�жϺ�����ڣ���tim.c������õ��������
	{
	 task_schedule_callback();
	}
	void tim2_IRQ(void)			//�жϺ�����ڣ���tim.c������õ��������
	{
		adcx1 = GET_ADC_PA1;       //��ȡADCƫ����
		adcx2 = GET_ADC_PA0;       //��ȡADC�ɼ���
		ADC_COJ = adcx2 - adcx1;
		ADC_index++;
		if(ADC_index > 1000){
		   ADC_index = 0;
		   adc_real  = (ADC_SUM+500) / 1000;   //��������
		   ADC_SUM = 0;
		}
		else{
			ADC_SUM += ADC_COJ;

		}

	}

	    int i,j;
int Dis_get(uint8_t *buff){
		int a,b,c,d;   //��ʱ����,��¼ǧλ��λʮλ��λ����
        a = (buff[1] - '0')*1000;
		b = (buff[2] - '0')*100;
		c = (buff[3] - '0')*10;
		d =  buff[4] - '0';

    return a+b+c+d;    
}

int str_num(uint8_t *buff){
	int a,b,c,d;   //��ʱ����,��¼ǧλ��λʮλ��λ����
    if(buff[3]!=0x00){
	    a = (buff[1] - '0')*100;
		b = (buff[2] - '0')*10;
		c = (buff[3] - '0')*1;
	}
	else {
	    a = (buff[1] - '0')*10;
		b = (buff[2] - '0')*1;
		c = 0;
	}

	return a+b+c+d;
}